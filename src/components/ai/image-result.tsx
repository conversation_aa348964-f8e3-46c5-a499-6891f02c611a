'use client';

import { Button } from '@/components/ui/button';
import type { ImageGenerationResponse } from '@/lib/ai/types';
import { AlertCircle, Download } from 'lucide-react';
import { useTranslations } from 'next-intl';
import Image from 'next/image';

interface ImageResultProps {
  result: ImageGenerationResponse | null;
  loading: boolean;
  error: string | null;
}

export function ImageResult({ result, loading, error }: ImageResultProps) {
  const t = useTranslations('AIApps.common');

  return (
    <div className="h-full w-full flex flex-col space-y-4">
      <div className="flex-1 min-h-[600px] bg-muted/20 rounded-lg border border-dashed border-border flex items-center justify-center">
        {result ? (
          <div className="relative rounded-lg overflow-hidden bg-gray-50 flex-1 flex items-center justify-center max-h-full">
            <Image
              src={result.imageUrl}
              alt="Generated figurine"
              width={512}
              height={512}
              className="max-w-full max-h-full object-contain"
              unoptimized
            />
          </div>
        ) : loading ? (
          <div className="flex items-center justify-center h-full">
            <div className="text-center space-y-4">
              <div className="animate-spin h-12 w-12 border-3 border-gray-500 border-t-transparent rounded-full mx-auto" />
              <p className="text-lg font-medium text-gray-700">
                {t('generating')}...
              </p>
            </div>
          </div>
        ) : error ? (
          <div className="flex items-center justify-center h-full">
            <div className="text-center space-y-4 p-6">
              <AlertCircle className="h-12 w-12 text-red-500 mx-auto" />
              <div className="space-y-2">
                <p className="text-lg font-medium text-gray-700">
                  Generation Failed
                </p>
                <p className="text-sm text-red-600 max-w-sm mx-auto">{error}</p>
              </div>
            </div>
          </div>
        ) : (
          <div className="text-center space-y-4">
            <div className="space-y-2">
              <h4 className="text-lg font-medium text-foreground">
                {t('result.emptyTitle')}
              </h4>
              <p className="text-muted-foreground">
                {t('result.emptyDescription')}
              </p>
            </div>
          </div>
        )}
      </div>
      {result && (
        <Button
          onClick={async () => {
            if (!result?.imageUrl) return;
            try {
              const downloadUrl = `/api/ai/download-image?url=${encodeURIComponent(result.imageUrl)}`;
              const a = document.createElement('a');
              a.href = downloadUrl;
              a.download = `figurine-${Date.now()}.png`;
              document.body.appendChild(a);
              a.click();
              document.body.removeChild(a);
            } catch (error) {
              console.error('Failed to download image:', error);
            }
          }}
          variant="outline"
          className="w-full border border-gray-300 text-gray-700 hover:bg-gray-100 cursor-pointer"
        >
          <Download className="size-4 mr-2" />
          {t('download')}
        </Button>
      )}
    </div>
  );
}
